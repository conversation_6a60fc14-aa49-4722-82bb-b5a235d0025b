# 气象数据解析和存储系统 (Weather Data Parser & Storage)

这是一个用于解析中国气象局A文件格式并存储到数据库的Python系统。A文件是地面气象观测数据文件的标准格式，包含台站参数、观测数据、质量控制和附加信息四个部分。

## 新增功能

- 🆕 **数据库存储** - 将解析的数据存储到SQLite、MySQL或PostgreSQL数据库
- 🆕 **小时和日数据分离** - 分别存储小时观测数据和日统计数据
- 🆕 **按要素分类存储** - 温度、降水、风等要素分别建表存储
- 🆕 **批量处理** - 支持批量处理整个目录的A文件
- 🆕 **数据查询** - 提供便捷的数据查询功能
- 🆕 **命令行工具** - 完整的命令行界面

## 功能特性

- ✅ **完整的A文件格式支持** - 支持A文件格式规范中定义的所有部分
- ✅ **台站参数解析** - 解析区站号、经纬度、高度等台站基本信息
- ✅ **20个气象要素解析** - 支持气压、气温、湿度、降水等所有标准气象要素
- ✅ **质量控制信息** - 解析质量控制码和更正数据
- ✅ **附加信息处理** - 处理月报封面、纪要、天气概况、备注等信息
- ✅ **数据验证** - 文件名格式验证、数据完整性检查
- ✅ **多编码支持** - 自动检测和处理GBK、UTF-8等编码格式
- ✅ **详细的数据结构** - 使用dataclass定义清晰的数据结构

## 文件结构

```
weather_parser/
├── data_structures.py    # 数据结构定义
├── utils.py             # 工具函数
├── weather_parser.py    # 主解析器类
├── example.py          # 使用示例
├── test_parser.py      # 测试模块
├── README.md           # 说明文档
└── data/               # 测试数据目录
    ├── A54424-195903.TXT
    ├── A54424-195908.TXT
    └── ...
```

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 数据库存储使用

#### 1. 处理整个数据目录

```bash
python main.py --data-dir data
```

#### 2. 处理单个文件

```bash
python main.py --single-file data/A54424-195901.TXT
```

#### 3. 列出所有台站

```bash
python main.py --list-stations
```

#### 4. 查询数据

```bash
python main.py --query-data "54424,1959-01-01,1959-01-31"
```

#### 5. 使用MySQL数据库

```bash
python main.py --database-url "mysql+pymysql://user:password@localhost:3306/weather_data"
```

### 基本解析使用

```python
from weather_parser import AFileParser

# 创建解析器实例
parser = AFileParser()

# 解析A文件
data = parser.parse_file("data/A54424-195903.TXT")

# 访问台站信息
print(f"台站号: {data.station_info.station_id}")
print(f"时间: {data.station_info.year}-{data.station_info.month:02d}")

# 访问气象要素
temp_element = data.get_element('T')  # 获取气温数据
if temp_element:
    print(f"气温数据段数: {len(temp_element.data_segments)}")

# 获取所有要素列表
elements = data.get_element_names()
for element in elements:
    print(element)
```

### 详细示例

```python
from weather_parser import AFileParser
from utils import parse_latitude, parse_longitude

parser = AFileParser()
data = parser.parse_file("data/A54424-195903.TXT")

# 解析地理坐标
lat_info = parse_latitude(data.station_info.latitude)
lon_info = parse_longitude(data.station_info.longitude)

print(f"纬度: {lat_info['decimal']:.4f}°")
print(f"经度: {lon_info['decimal']:.4f}°")

# 访问特定要素的数据
elements_to_check = ['P', 'T', 'U', 'R', 'W']  # 气压、气温、湿度、降水、天气现象

for element_code in elements_to_check:
    element = data.get_element(element_code)
    if element:
        print(f"\n{element_code}要素:")
        print(f"  方式位: {element.mode_code}")
        print(f"  数据段数: {len(element.data_segments)}")

        # 显示第一段的前几条记录
        if element.data_segments:
            first_segment = element.data_segments[0]
            for i, record in enumerate(first_segment[:3]):
                print(f"  记录{i+1}: {record}")
```

## 数据库表结构

### 主要表
- **`stations`** - 台站信息表：存储台站基本信息（区站号、经纬度、海拔等）
- **`hourly_data`** - 小时观测数据表：存储逐时观测记录
- **`daily_data`** - 日统计数据表：存储日最值、平均值等统计数据

### 专业要素表
- **`temperature_data`** - 温度数据表：气温、地温、草面温度等
- **`precipitation_data`** - 降水数据表：降水量、降水强度等
- **`wind_data`** - 风数据表：风向、风速、极大风速等

### 数据组织方式

根据用户需求，数据按以下方式组织：

#### 按台站和时间点组织
- 每个台站的数据独立存储
- 小时数据表提供逐时观测记录
- 日数据表提供日统计信息
- 支持按时间范围查询特定台站数据

#### 按要素分类
- 温度相关数据存储在专门的温度表
- 降水数据存储在降水表
- 风数据存储在风表
- 便于进行专业气象分析

## 数据结构说明

### 主要数据类

- **`AFileData`** - 完整的A文件数据
- **`StationInfo`** - 台站参数信息
- **`WeatherElement`** - 气象要素数据
- **`QualityControlData`** - 质量控制信息
- **`AdditionalInfo`** - 附加信息

### 气象要素代码

| 代码 | 要素名称 | 代码 | 要素名称 |
|------|----------|------|----------|
| P | 气压 | L | 蒸发量 |
| T | 气温 | Z | 积雪 |
| I | 湿球温度 | G | 电线积冰 |
| E | 水汽压 | F | 风 |
| U | 相对湿度 | D | 浅层地温 |
| N | 云量 | K | 深层地温 |
| H | 云高 | A | 冻土深度 |
| C | 云状 | S | 日照时数 |
| V | 能见度 | B | 草面(雪面)温度 |
| R | 降水量 | W | 天气现象 |

## 工具函数

### 地理信息解析

```python
from utils import parse_latitude, parse_longitude, parse_height

# 解析纬度 (格式: DDMMX)
lat_info = parse_latitude("4008N")
# 返回: {'degrees': '40', 'minutes': '08', 'direction': 'N', 'decimal': 40.133}

# 解析经度 (格式: DDDMMX)
lon_info = parse_longitude("11706E")
# 返回: {'degrees': '117', 'minutes': '06', 'direction': 'E', 'decimal': 117.1}

# 解析高度 (格式: HHHHHH)
height_info = parse_height("000266")
# 返回: {'parameter': '0', 'value': 26.6, 'unit': 'm'}
```

### 文件名验证

```python
from utils import validate_filename, extract_date_from_filename

# 验证文件名格式
is_valid = validate_filename("A54424-195903.TXT")  # True

# 提取日期信息
year, month = extract_date_from_filename("A54424-195903.TXT")  # (1959, 3)
```

## 运行测试

### 运行示例

```bash
python example.py
```

### 运行测试套件

```bash
python test_parser.py
```

### 测试单个文件

```python
from weather_parser import AFileParser

parser = AFileParser()
data = parser.parse_file("data/A54424-195903.TXT")

print(f"解析成功!")
print(f"台站: {data.station_info.station_id}")
print(f"要素数量: {len(data.weather_elements)}")
```

## 错误处理

解析器包含完善的错误处理机制：

```python
try:
    data = parser.parse_file("path/to/file.txt")
except FileNotFoundError:
    print("文件不存在")
except ValueError as e:
    print(f"数据格式错误: {e}")
except UnicodeDecodeError:
    print("文件编码错误")
except Exception as e:
    print(f"解析失败: {e}")
```

## 注意事项

1. **文件编码**: 解析器会自动尝试GBK、UTF-8、Latin1编码
2. **文件格式**: 严格按照A文件格式规范进行解析
3. **缺测数据**: 用'MISSING'表示缺测，'TRACE'表示微量
4. **内存使用**: 大文件解析时注意内存使用情况

## 扩展开发

如需扩展功能，可以：

1. 在`data_structures.py`中添加新的数据结构
2. 在`utils.py`中添加新的工具函数
3. 在`weather_parser.py`中扩展解析逻辑
4. 在`test_parser.py`中添加相应测试

## 许可证

本项目采用MIT许可证。
