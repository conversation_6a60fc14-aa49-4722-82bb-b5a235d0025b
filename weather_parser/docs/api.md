# API 文档

Weather Parser 提供了完整的Python API，用于在代码中集成气象数据解析功能。

## 核心类

### WeatherFileParser

主要的文件解析器类。

```python
from weather_parser import WeatherFileParser

parser = WeatherFileParser()
result = parser.parse_file('data.TXT')
```

#### 方法

##### `parse_file(file_path: Path) -> ParseResult`

解析单个A文件。

**参数：**
- `file_path`: 要解析的文件路径

**返回：**
- `ParseResult`: 解析结果对象

**示例：**
```python
from pathlib import Path
parser = WeatherFileParser()
result = parser.parse_file(Path('weather_data.TXT'))

if result.success:
    print(f"解析成功: {result.station_info.station_id}")
    for element in result.elements:
        print(f"要素: {element.element_type}, 值: {element.value}")
else:
    print(f"解析失败: {result.errors}")
```

##### `parse_multiple_files(file_paths: List[Path]) -> List[ParseResult]`

批量解析多个文件。

**参数：**
- `file_paths`: 文件路径列表

**返回：**
- `List[ParseResult]`: 解析结果列表

### ParseResult

解析结果数据类。

#### 属性

- `file_path: Path` - 文件路径
- `success: bool` - 解析是否成功
- `station_info: Optional[StationInfo]` - 台站信息
- `file_info: Optional[FileInfo]` - 文件信息
- `elements: List[ElementData]` - 气象要素数据
- `errors: List[str]` - 错误信息
- `warnings: List[str]` - 警告信息
- `parse_time: float` - 解析用时（秒）

### StationInfo

台站信息数据类。

#### 属性

- `station_id: str` - 台站编号
- `latitude: float` - 纬度
- `longitude: float` - 经度
- `observation_altitude: float` - 观测场海拔高度
- `station_altitude: Optional[float]` - 台站海拔高度

### ElementData

气象要素数据类。

#### 属性

- `element_type: str` - 要素类型
- `element_code: str` - 要素代码  
- `value: Any` - 要素值
- `unit: Optional[str]` - 单位
- `quality_code: Optional[str]` - 质量码
- `description: Optional[str]` - 描述

## 验证器

### DataValidator

数据验证器类。

```python
from weather_parser.validators import DataValidator

validator = DataValidator()
result = validator.validate_single_observation(parse_result)
```

#### 方法

##### `validate_single_observation(data: ParseResult) -> Dict`

验证单个观测数据。

**参数：**
- `data`: 解析结果对象

**返回：**
- `Dict`: 验证结果字典

**返回格式：**
```python
{
    'valid': bool,  # 是否通过验证
    'summary': {
        'total_elements': int,  # 总要素数
        'valid_elements': int,  # 有效要素数
        'missing_elements': int,  # 缺失要素数
        'invalid_elements': int  # 无效要素数
    },
    'errors': List[str],  # 错误列表
    'warnings': List[str]  # 警告列表
}
```

##### `validate_batch(data_list: List[ParseResult]) -> List[Dict]`

批量验证多个观测数据。

### QualityChecker

数据质量检查器。

```python
from weather_parser.validators import QualityChecker

checker = QualityChecker()
quality = checker.check_single_observation(parse_result)
```

#### 方法

##### `check_single_observation(data: ParseResult) -> Dict`

检查单个观测数据质量。

**返回格式：**
```python
{
    'grade': str,  # 质量等级 (A/B/C/D)
    'score': float,  # 质量分数 (0-100)
    'completeness': float,  # 完整性分数
    'consistency': float,  # 一致性分数
    'reliability': float,  # 可靠性分数
    'issues': List[str]  # 发现的问题
}
```

## 导出器

### ExportManager

导出管理器。

```python
from weather_parser.exporters import ExportManager, ExportOptions

export_manager = ExportManager()
options = ExportOptions(output_path=Path('output.csv'))
result = export_manager.export(parse_result, 'csv', options)
```

#### 方法

##### `export(data: ParseResult, format: str, options: ExportOptions) -> Dict`

导出解析结果。

**参数：**
- `data`: 解析结果对象
- `format`: 导出格式 ('csv', 'json', 'sql')
- `options`: 导出选项

**返回：**
- `Dict`: 导出结果信息

### ExportOptions

导出选项配置。

#### 属性

- `output_path: Path` - 输出文件路径
- `include_metadata: bool = True` - 是否包含元数据
- `compression: Optional[str] = None` - 压缩格式
- `encoding: str = 'utf-8'` - 文件编码
- `custom_options: Dict = field(default_factory=dict)` - 自定义选项

### CSV导出器

```python
from weather_parser.exporters import CSVExporter

exporter = CSVExporter()
exporter.export(parse_result, export_options)
```

#### 选项

通过 `custom_options` 传递：
- `delimiter: str = ','` - 分隔符
- `quoting: int = csv.QUOTE_MINIMAL` - 引用方式
- `date_format: str = '%Y-%m-%d %H:%M:%S'` - 日期格式

### JSON导出器

```python
from weather_parser.exporters import JSONExporter

exporter = JSONExporter()
exporter.export(parse_result, export_options)
```

#### 选项

通过 `custom_options` 传递：
- `indent: int = 2` - 缩进空格数
- `ensure_ascii: bool = False` - 是否确保ASCII编码
- `compact: bool = False` - 是否压缩格式

### SQL导出器

```python
from weather_parser.exporters import SQLExporter

exporter = SQLExporter()
exporter.export(parse_result, export_options)
```

#### 选项

通过 `custom_options` 传递：
- `table_name: str = 'weather_data'` - 表名
- `include_create_table: bool = True` - 是否包含建表语句
- `batch_size: int = 1000` - 批量插入大小

## 配置管理

### ConfigManager

配置管理器。

```python
from weather_parser.config import ConfigManager

# 加载默认配置
config = ConfigManager()

# 从文件加载配置
config = ConfigManager.from_file(Path('config.yaml'))

# 查找配置文件
config_path = ConfigManager.find_config_file()
```

#### 方法

##### `from_file(config_path: Path) -> ConfigManager`

从文件加载配置。

##### `save_to_file(config_path: Path) -> None`

保存配置到文件。

##### `update(updates: Dict) -> None`

更新配置。

##### `find_config_file() -> Optional[Path]`

查找配置文件。

## 错误处理

### ParsingError

解析错误异常。

```python
from weather_parser.exceptions import ParsingError

try:
    result = parser.parse_file('invalid_file.txt')
except ParsingError as e:
    print(f"解析错误: {e}")
```

### ValidationError

验证错误异常。

```python
from weather_parser.exceptions import ValidationError

try:
    validator.validate_strict(data)
except ValidationError as e:
    print(f"验证错误: {e}")
```

### ExportError

导出错误异常。

```python
from weather_parser.exceptions import ExportError

try:
    export_manager.export(data, 'invalid_format', options)
except ExportError as e:
    print(f"导出错误: {e}")
```

## 使用示例

### 完整处理流程

```python
from pathlib import Path
from weather_parser import WeatherFileParser
from weather_parser.validators import DataValidator, QualityChecker
from weather_parser.exporters import ExportManager, ExportOptions
from weather_parser.config import ConfigManager

# 1. 加载配置
config = ConfigManager.from_file(Path('config.yaml'))

# 2. 创建解析器
parser = WeatherFileParser()

# 3. 解析文件
result = parser.parse_file(Path('weather_data.TXT'))

if result.success:
    # 4. 数据验证
    validator = DataValidator()
    validation = validator.validate_single_observation(result)
    
    if validation['valid']:
        # 5. 质量检查
        checker = QualityChecker()
        quality = checker.check_single_observation(result)
        
        print(f"数据质量等级: {quality['grade']}")
        print(f"质量分数: {quality['score']}")
        
        # 6. 导出数据
        export_manager = ExportManager()
        
        # CSV导出
        csv_options = ExportOptions(
            output_path=Path('output.csv'),
            include_metadata=True,
            custom_options={'delimiter': ','}
        )
        export_manager.export(result, 'csv', csv_options)
        
        # JSON导出
        json_options = ExportOptions(
            output_path=Path('output.json'),
            include_metadata=True,
            custom_options={'indent': 2}
        )
        export_manager.export(result, 'json', json_options)
        
    else:
        print(f"数据验证失败: {validation['errors']}")
else:
    print(f"解析失败: {result.errors}")
```

### 批量处理

```python
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from weather_parser import WeatherFileParser

def process_file(file_path):
    parser = WeatherFileParser()
    result = parser.parse_file(file_path)
    return file_path, result

# 获取所有A文件
input_dir = Path('input_data')
files = list(input_dir.glob('*.TXT'))

# 并发处理
with ThreadPoolExecutor(max_workers=4) as executor:
    results = list(executor.map(process_file, files))

# 处理结果
successful = 0
failed = 0

for file_path, result in results:
    if result.success:
        successful += 1
        print(f"✅ {file_path.name}: {len(result.elements)} 个要素")
    else:
        failed += 1
        print(f"❌ {file_path.name}: {result.errors}")

print(f"\n处理完成: 成功 {successful}, 失败 {failed}")
```

### 自定义解析器

```python
from weather_parser.parsers.base_parser import BaseElementParser
from weather_parser.models import ElementData

class CustomElementParser(BaseElementParser):
    """自定义要素解析器"""
    
    def parse(self, line: str, line_number: int) -> List[ElementData]:
        # 实现自定义解析逻辑
        elements = []
        
        # 解析逻辑...
        element = ElementData(
            element_type="custom_element",
            element_code="CE01",
            value=parsed_value,
            unit="unit"
        )
        elements.append(element)
        
        return elements

# 使用自定义解析器
parser = WeatherFileParser()
parser.register_parser("custom", CustomElementParser())
```

## 类型注解

Weather Parser 使用完整的类型注解，支持静态类型检查：

```python
from typing import List, Optional, Dict, Any
from pathlib import Path
from weather_parser import WeatherFileParser
from weather_parser.models import ParseResult, ElementData

def process_weather_data(
    file_paths: List[Path], 
    output_format: str = 'csv'
) -> Dict[str, Any]:
    """处理气象数据文件"""
    parser: WeatherFileParser = WeatherFileParser()
    results: List[ParseResult] = []
    
    for file_path in file_paths:
        result: ParseResult = parser.parse_file(file_path)
        results.append(result)
    
    return {
        'total_files': len(file_paths),
        'successful_parses': sum(1 for r in results if r.success),
        'total_elements': sum(len(r.elements) for r in results if r.success)
    }
``` 