"""
云况和能见度解析器测试模块
"""

import pytest
from weather_parser.parsers.cloud_visibility_parser import CloudVisibilityParser
from weather_parser.models import ParseContext
from weather_parser.exceptions import DataValidationError
from pathlib import Path


class TestCloudVisibilityParser:
    """云况和能见度解析器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.parser = CloudVisibilityParser()
        self.context = ParseContext(
            file_path=Path("data/test.txt")
        )
    
    def test_convert_cloud_amount(self):
        """测试云量值转换"""
        # 正常数值（0-8成）
        assert self.parser._convert_cloud_amount("0") == 0
        assert self.parser._convert_cloud_amount("4") == 4
        assert self.parser._convert_cloud_amount("8") == 8
        
        # 缺测值
        assert self.parser._convert_cloud_amount("////") is None
        assert self.parser._convert_cloud_amount("99") is None
        assert self.parser._convert_cloud_amount("--") is None
        
        # 超出范围
        assert self.parser._convert_cloud_amount("9") is None
        assert self.parser._convert_cloud_amount("-1") is None
        
        # 无效格式
        assert self.parser._convert_cloud_amount("abc") is None
        assert self.parser._convert_cloud_amount("") is None
    
    def test_convert_cloud_height(self):
        """测试云高值转换"""
        # 正常格式（云状2位+高度）
        result = self.parser._convert_cloud_height("Ci1500")
        assert result is not None
        assert result['cloud_type'] == "卷云"  # Ci映射
        assert result['height'] == 1500
        
        # 未知云状符号
        result = self.parser._convert_cloud_height("XX2000")
        assert result is not None
        assert result['cloud_type'] == "XX"  # 保持原值
        assert result['height'] == 2000
        
        # 缺测值
        assert self.parser._convert_cloud_height("////") is None
        assert self.parser._convert_cloud_height("9999") is None
        
        # 无效格式
        assert self.parser._convert_cloud_height("Ci") is None  # 太短
        assert self.parser._convert_cloud_height("abc") is None
    
    def test_convert_cloud_type(self):
        """测试云状转换"""
        # 标准云状符号
        assert self.parser._convert_cloud_type("Ci") == "卷云"
        assert self.parser._convert_cloud_type("Cu") == "积云"
        assert self.parser._convert_cloud_type("Cb") == "积雨云"
        
        # 未知符号（保持原值）
        assert self.parser._convert_cloud_type("XX") == "XX"
        
        # 缺测值
        assert self.parser._convert_cloud_type("///") is None
        assert self.parser._convert_cloud_type("---") is None
        assert self.parser._convert_cloud_type("") is None
    
    def test_convert_visibility(self):
        """测试能见度转换"""
        # 3位距离格式
        result = self.parser._convert_visibility("150")
        assert result is not None
        assert result['distance'] == 15.0  # 150 * 0.1
        assert '10-20km' in result['level']  # 15km属于10-20km级别
        
        # 4位距离+级别格式
        result = self.parser._convert_visibility("0505")
        assert result is not None
        assert result['distance'] == 5.0  # 050 * 0.1
        assert result['level'] == "2-4km"  # 级别5
        
        # 缺测值
        assert self.parser._convert_visibility("////") is None
        assert self.parser._convert_visibility("9999") is None
        
        # 无效格式
        assert self.parser._convert_visibility("abc") is None
        assert self.parser._convert_visibility("12345") is None  # 太长
    
    def test_distance_to_level(self):
        """测试距离到级别的转换"""
        assert "50m以下" in self.parser._distance_to_level(0.03)
        assert "50-200m" in self.parser._distance_to_level(0.1)
        assert "1-2km" in self.parser._distance_to_level(1.5)
        assert "50km以上" in self.parser._distance_to_level(100.0)
    
    def test_parse_cloud_amount_data(self):
        """测试云量数据解析"""
        lines = [
            "2 4 6 8",
            "0 1 3 5 7.",
            "8 7 6 5"
        ]
        
        result = self.parser.parse(lines, "N", "A", self.context)
        
        assert result.element_code == "N"
        assert result.element_name == "云量"
        assert result.mode == "A"
        assert len(result.segments) >= 3  # 3个数据段 + 统计段
        
        # 检查第一行解析
        first_segment = result.segments[0]
        assert first_segment['cloud_amounts'] == [2, 4, 6, 8]
        assert first_segment['valid_count'] == 4
        assert first_segment['missing_count'] == 0
    
    def test_parse_visibility_data(self):
        """测试能见度数据解析"""
        lines = [
            "150 200 350",
            "0505 1208 2509.",
            "9999 //// 050"
        ]
        
        result = self.parser.parse(lines, "V", "B", self.context)
        
        assert result.element_code == "V"
        assert result.element_name == "能见度"
        assert result.mode == "B"
        assert len(result.segments) >= 3
        
        # 检查能见度值
        first_segment = result.segments[0]
        assert len(first_segment['visibility_values']) == 3
        assert first_segment['visibility_values'][0] == 15.0  # 150 * 0.1
        
        # 检查带级别的格式
        second_segment = result.segments[1]
        assert second_segment['visibility_values'][0] == 5.0  # 050 * 0.1
        assert "2-4km" in second_segment['visibility_levels'][0]  # 级别5
    
    def test_parse_empty_data(self):
        """测试空数据解析"""
        lines = ["=", "", "   "]
        
        result = self.parser.parse(lines, "N", "A", self.context)
        
        # 应该没有数据段（因为没有有效数据，也不会生成统计段）
        assert len(result.segments) == 0  # 没有数据段
        assert len(result.warnings) > 0
        assert "未找到有效的云量数据段" in result.warnings[0]
    
    def test_invalid_element_code(self):
        """测试无效要素代码"""
        lines = ["2 4 6"]
        
        with pytest.raises(DataValidationError) as exc_info:
            self.parser.parse(lines, "X", "A", self.context)
        
        assert "不支持的云况能见度要素代码: X" in str(exc_info.value)
    
    def test_invalid_mode_error(self):
        """测试无效方式位错误"""
        lines = ["2 4 6"]
        
        with pytest.raises(DataValidationError) as exc_info:
            self.parser.parse(lines, "N", "X", self.context)
        
        assert "要素N不支持方式位: X" in str(exc_info.value)
    
    def test_data_completeness_warning(self):
        """测试数据完整性警告"""
        lines = [
            "2 //// 6 ////",
            "9999 1 //// 5",
            "//// //// //// ////"
        ]
        
        result = self.parser.parse(lines, "N", "A", self.context)
        
        # 应该有完整性警告
        assert len(result.warnings) > 0
        warning_messages = ' '.join(result.warnings)
        assert ("完整性较低" in warning_messages)
    
    def test_summary_statistics(self):
        """测试统计信息"""
        lines = [
            "2 4 6 8",
            "0 1 3 5"
        ]
        
        result = self.parser.parse(lines, "N", "A", self.context)
        
        # 找到统计段
        summary_segment = None
        for segment in result.segments:
            if segment.get('summary'):
                summary_segment = segment
                break
        
        assert summary_segment is not None
        assert summary_segment['total_values'] == 8  # 2行 * 4个值
        assert summary_segment['valid_values'] == 8  # 全部有效
        assert summary_segment['completeness_percent'] == 100.0
        assert summary_segment['element_info']['code'] == "N"
        assert summary_segment['element_info']['name'] == "云量" 