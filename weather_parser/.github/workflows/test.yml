name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11']
        exclude:
          # 排除一些组合以减少构建时间
          - os: macos-latest
            python-version: '3.8'
          - os: windows-latest
            python-version: '3.8'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip packages
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 src/ tests/ --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 src/ tests/ --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Check formatting with black
      run: |
        black --check src/ tests/

    - name: Check import sorting with isort
      run: |
        isort --check-only src/ tests/

    - name: Type checking with mypy
      run: |
        mypy src/weather_parser/

    - name: Test with pytest
      run: |
        pytest --cov=weather_parser --cov-report=xml --cov-report=html -v

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit[toml] safety

    - name: Security check with bandit
      run: |
        bandit -r src/ -f json -o bandit-report.json || true
        bandit -r src/ --severity-level medium

    - name: Safety check
      run: |
        safety check --json --output safety-report.json || true
        safety check

  performance:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Performance tests
      run: |
        pytest tests/ -m performance -v

    - name: Benchmark tests
      run: |
        python -m pytest tests/test_integration.py::TestPerformance -v --tb=short

  integration:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Integration tests
      run: |
        pytest tests/ -m integration -v

    - name: CLI integration tests
      run: |
        # 测试CLI命令的基本功能
        weather-parser --help
        weather-parser --version
        
        # 创建测试数据
        mkdir -p test_data
        echo "SURF_CHN_MUL_HOR_N 2023010108" > test_data/test.TXT
        echo "54511 2023 01 01 08 116.47 39.93 55.1 55.1 10132 10108 0 -999 -999 -999 -999 -999" >> test_data/test.TXT
        echo "2 2 2 -25 75 01 3 0 0 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999" >> test_data/test.TXT
        echo "-999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999" >> test_data/test.TXT
        echo "-999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999 -999" >> test_data/test.TXT
        
        # 测试解析命令
        weather-parser parse test_data/test.TXT --output test_output.csv --format csv
        
        # 检查输出文件
        test -f test_output.csv || exit 1
        
        # 测试配置命令
        weather-parser config --show

  docs:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[docs]"

    - name: Build documentation
      run: |
        cd docs
        make html

    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/_build/html

  package:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: |
        python -m build

    - name: Check package
      run: |
        twine check dist/*

    - name: Publish to Test PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.TEST_PYPI_API_TOKEN }}
      run: |
        twine upload --repository testpypi dist/*

    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        twine upload dist/*

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false 