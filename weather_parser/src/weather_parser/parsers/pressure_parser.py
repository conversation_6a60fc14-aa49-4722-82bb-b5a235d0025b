"""
气压数据解析器模块

解析A文件中的气压（P）要素数据，支持不同方式位的本站气压和海平面气压。
"""

import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from loguru import logger

from ..models import ElementData, ParseContext
from ..exceptions import DataValidationError, FileFormatError
from ..constants import PRESSURE_MODES, MISSING_VALUE_INDICATORS


class PressureParser:
    """气压数据解析器
    
    支持的方式位：
    - C: 定时观测本站气压
    - 3: 3时观测本站气压
    - 4: 4次定时观测本站气压  
    - 6: 6次定时观测本站气压
    - 8: 8次定时观测本站气压
    - B: 定时观测本站气压（备用格式）
    - D: 定时观测海平面气压
    
    数据格式：
    - 数据值为0.1hPa的整数形式（如0160表示1016.0hPa）
    - 极值和时间信息在行末
    - 支持缺测值（9999、////等）
    """
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logger.bind(parser="PressureParser")
        
        # 气压数据行的正则表达式模式
        self.data_line_pattern = re.compile(
            r'^((?:\d{4}|\/{4}|9{4})\s*)+' # 主要数据部分（4位数字或缺测标记）
            r'(?:\s+(\d{4})\s+(\d{4})\s+(\d{4})\s+(\d{4})\.?)?$'  # 可选的极值和时间信息
        )
        
        # 支持的方式位映射
        self.mode_mapping = {
            'C': 'continuous_pressure',    # 定时观测本站气压
            '3': 'tri_daily_pressure',     # 3时观测本站气压
            '4': 'four_times_pressure',    # 4次定时观测本站气压
            '6': 'six_times_pressure',     # 6次定时观测本站气压
            '8': 'eight_times_pressure',   # 8次定时观测本站气压
            'B': 'backup_pressure',        # 定时观测本站气压（备用）
            'D': 'sea_level_pressure'      # 定时观测海平面气压
        }
    
    def parse(self, lines: List[str], mode: str, context: ParseContext) -> ElementData:
        """
        解析气压数据
        
        Args:
            lines: 气压数据行列表（不包括方式位行）
            mode: 方式位（C、3、4、6、8、B、D）
            context: 解析上下文
            
        Returns:
            ElementData对象
            
        Raises:
            FileFormatError: 格式错误
            DataValidationError: 数据验证错误
        """
        self.logger.debug(f"开始解析气压数据，方式位: {mode}, 行数: {len(lines)}")
        
        # 验证方式位
        if mode not in self.mode_mapping:
            raise DataValidationError(
                f"不支持的气压方式位: {mode}",
                element_code="P",
                value=mode
            )
        
        element_data = ElementData(
            element_code="P",
            element_name="气压",
            mode=mode,
            segments=[]
        )
        
        # 解析每一行数据
        for i, line in enumerate(lines):
            try:
                segment = self._parse_data_line(line.strip(), i + 1, mode)
                if segment:
                    element_data.segments.append(segment)
                    
            except Exception as e:
                error_msg = f"解析第{i+1}行气压数据失败: {e}"
                element_data.errors.append(error_msg)
                self.logger.warning(error_msg)
        
        # 验证解析结果
        self._validate_parsed_data(element_data, mode)
        
        self.logger.info(
            f"气压数据解析完成: 方式位{mode}, "
            f"有效段数{len(element_data.segments)}, "
            f"错误数{len(element_data.errors)}"
        )
        
        return element_data
    
    def _parse_data_line(self, line: str, line_number: int, mode: str) -> Optional[Dict[str, Any]]:
        """
        解析单行气压数据
        
        Args:
            line: 数据行内容
            line_number: 行号
            mode: 方式位
            
        Returns:
            解析后的数据段字典，如果行为空或无效则返回None
        """
        if not line or line.isspace():
            return None
        
        # 简化解析：根据实际数据格式分割
        # 格式：数值序列 + 可选的极值和时间
        parts = line.split()
        if not parts:
            return None
        
        # 查找极值和时间信息的位置（通常在末尾，以点结束）
        extremes_start = -1
        for i in range(len(parts)):
            if parts[i].endswith('.'):
                # 找到以点结尾的部分，往前推4个位置作为极值起始
                extremes_start = max(0, i - 3)
                break
        
        # 分离主要数据和极值信息
        if extremes_start > 0:
            data_parts = parts[:extremes_start]
            extreme_parts = parts[extremes_start:]
        else:
            data_parts = parts
            extreme_parts = []
        
        # 解析主要数据值
        pressure_values = []
        for part in data_parts:
            pressure_value = self._convert_pressure_value(part)
            pressure_values.append(pressure_value)
        
        # 构建数据段
        segment = {
            'line_number': line_number,
            'raw_line': line,
            'pressure_values': pressure_values,
            'valid_count': len([v for v in pressure_values if v is not None]),
            'missing_count': len([v for v in pressure_values if v is None])
        }
        
        # 解析极值信息（如果存在）
        if len(extreme_parts) >= 4:
            try:
                max_value = extreme_parts[0]
                max_time = extreme_parts[1]
                min_value = extreme_parts[2]
                min_time = extreme_parts[3].rstrip('.')
                
                segment['extremes'] = {
                    'max_pressure': self._convert_pressure_value(max_value),
                    'max_time': self._parse_time(max_time),
                    'min_pressure': self._convert_pressure_value(min_value),
                    'min_time': self._parse_time(min_time)
                }
            except (IndexError, ValueError):
                # 极值解析失败，不影响主数据
                pass
        
        return segment
    

    
    def _convert_pressure_value(self, value_str: str) -> Optional[float]:
        """
        转换气压值字符串为数值
        
        Args:
            value_str: 气压值字符串（4位）
            
        Returns:
            气压值（hPa），缺测时返回None
        """
        if not value_str or value_str.strip() in MISSING_VALUE_INDICATORS:
            return None
        
        # 清理字符串
        clean_value = value_str.strip()
        
        # 检查是否为缺测标记
        if clean_value in ['////', '9999', '----']:
            return None
        
        try:
            # 转换为整数，然后除以10得到hPa值
            raw_value = int(clean_value)
            
            # 处理特殊的缺测值
            if raw_value >= 9990:
                return None
            
            # 0.1hPa转hPa
            pressure_hpa = raw_value / 10.0
            
            # 合理性检查和处理
            if pressure_hpa < 800:
                # 可能是海平面气压表示法，但要排除明显异常值
                if 1 <= raw_value <= 300:  # 只有这个范围才认为是海平面气压格式 
                    pressure_hpa = 1000 + pressure_hpa
                else:
                    return None  # 值不合理（包括500.0这样的异常值）
            elif pressure_hpa > 1200:
                return None  # 值过大，不合理
            
            return pressure_hpa
            
        except (ValueError, TypeError):
            return None
    
    def _parse_time(self, time_str: str) -> Optional[str]:
        """
        解析时间字符串
        
        Args:
            time_str: 时间字符串（4位，格式HHMM）
            
        Returns:
            格式化的时间字符串（HH:MM），无效时返回None
        """
        if not time_str or len(time_str) != 4:
            return None
        
        try:
            hour = int(time_str[:2])
            minute = int(time_str[2:4])
            
            # 验证时间有效性
            if 0 <= hour <= 23 and 0 <= minute <= 59:
                return f"{hour:02d}:{minute:02d}"
            else:
                return None
                
        except (ValueError, TypeError):
            return None
    
    def _validate_parsed_data(self, element_data: ElementData, mode: str) -> None:
        """
        验证解析后的气压数据
        
        Args:
            element_data: 解析后的要素数据
            mode: 方式位
        """
        if not element_data.segments:
            element_data.warnings.append("未找到有效的气压数据段")
            return
        
        total_values = 0
        valid_values = 0
        
        for segment in element_data.segments:
            segment_values = len(segment.get('pressure_values', []))
            segment_valid = segment.get('valid_count', 0)
            
            total_values += segment_values
            valid_values += segment_valid
            
            # 检查数据合理性
            for pressure in segment.get('pressure_values', []):
                if pressure is not None:
                    if pressure < 800 or pressure > 1100:
                        element_data.warnings.append(
                            f"第{segment['line_number']}行存在异常气压值: {pressure}hPa"
                        )
        
        # 计算数据完整性
        if total_values > 0:
            completeness = (valid_values / total_values) * 100
            
            if completeness < 50:
                element_data.warnings.append(
                    f"气压数据完整性较低: {completeness:.1f}%"
                )
            
            # 添加统计信息
            element_data.segments.append({
                'line_number': 0,
                'summary': True,
                'total_values': total_values,
                'valid_values': valid_values,
                'completeness_percent': completeness,
                'mode_description': self.mode_mapping.get(mode, f"方式位{mode}")
            })


__all__ = ["PressureParser"] 