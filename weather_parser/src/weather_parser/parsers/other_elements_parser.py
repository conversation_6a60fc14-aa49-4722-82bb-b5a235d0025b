"""
其他要素解析器模块

处理蒸发量、积雪、电线积冰、冻土深度、日照时数、草面温度等要素的解析功能。
"""

import re
from typing import Dict, List, Optional, Union, Any
from datetime import time, date

from ..models import ElementData, ParseContext
from ..exceptions import DataValidationError, FileFormatError
from ..constants import MISSING_VALUE_INDICATORS
from loguru import logger


class OtherElementsParser:
    """其他要素解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logger.bind(parser="OtherElementsParser")
        
        # 支持的要素代码
        self.supported_elements = {
            "E": "蒸发量",
            "S": "积雪",
            "G": "电线积冰",
            "A": "冻土深度",
            "J": "日照时数",
            "B": "草面温度"
        }
        
        # 各要素支持的方式位
        self.element_modes = {
            "E": {  # 蒸发量
                "0": "定时蒸发",
                "1": "日蒸发量",
                "A": "旬蒸发量",
                "B": "月蒸发量"
            },
            "S": {  # 积雪
                "0": "积雪深度",
                "1": "积雪重量",
                "A": "雪况描述"
            },
            "G": {  # 电线积冰
                "0": "积冰厚度",
                "1": "积冰重量",
                "A": "积冰类型"
            },
            "A": {  # 冻土深度
                "0": "冻土深度",
                "1": "解冻深度",
                "A": "冻土状态"
            },
            "J": {  # 日照时数
                "0": "实际日照",
                "1": "可照时数",
                "A": "日照百分率"
            },
            "B": {  # 草面温度
                "0": "定时草温",
                "1": "日极值草温",
                "A": "月极值草温"
            }
        }
        
        # 积雪状态映射
        self.snow_conditions = {
            "0": "无雪",
            "1": "少量积雪",
            "2": "薄雪层",
            "3": "中等积雪",
            "4": "深厚积雪",
            "5": "极厚积雪",
            "6": "积雪融化",
            "7": "新雪",
            "8": "硬雪壳",
            "9": "结冰雪面"
        }
        
        # 电线积冰类型映射
        self.ice_types = {
            "0": "无积冰",
            "1": "白霜",
            "2": "软雾凇",
            "3": "硬雾凇",
            "4": "雨凇",
            "5": "湿雪",
            "6": "干雪",
            "7": "混合积冰",
            "8": "其他积冰",
            "9": "积冰脱落"
        }
        
        # 冻土状态映射
        self.frozen_soil_states = {
            "0": "未冻结",
            "1": "表层冻结",
            "2": "浅层冻结",
            "3": "深层冻结",
            "4": "全层冻结",
            "5": "开始解冻",
            "6": "部分解冻",
            "7": "大部解冻",
            "8": "完全解冻",
            "9": "季节性冻土"
        }
    
    def _convert_evaporation_value(self, evap_str: str) -> Optional[float]:
        """
        转换蒸发量值
        
        Args:
            evap_str: 蒸发量字符串
            
        Returns:
            蒸发量值（mm）或None
        """
        if not evap_str or evap_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        evap_str = evap_str.strip()
        
        try:
            # 0.1mm精度格式（如025表示2.5mm）
            if len(evap_str) == 3 and evap_str.isdigit():
                return float(evap_str) / 10.0
            
            # 直接数值
            if evap_str.isdigit():
                return float(evap_str)
            
            # 包含小数点
            if '.' in evap_str:
                return float(evap_str)
                
            # 特殊标记：痕迹
            if evap_str.upper() in ["T", "TR", "TRACE"]:
                return 0.0
                
        except ValueError:
            pass
            
        return None
    
    def _convert_snow_depth(self, depth_str: str) -> Optional[float]:
        """
        转换积雪深度值
        
        Args:
            depth_str: 积雪深度字符串
            
        Returns:
            积雪深度值（cm）或None
        """
        if not depth_str or depth_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        depth_str = depth_str.strip()
        
        try:
            # 0.1cm精度格式（优先检查3位数字）
            if len(depth_str) == 3 and depth_str.isdigit():
                return float(depth_str) / 10.0
                
            # 直接厘米数值
            if depth_str.isdigit():
                return float(depth_str)
            
            # 包含小数点
            if '.' in depth_str:
                return float(depth_str)
                
        except ValueError:
            pass
            
        return None
    
    def _convert_ice_thickness(self, thickness_str: str) -> Optional[float]:
        """
        转换积冰厚度值
        
        Args:
            thickness_str: 积冰厚度字符串
            
        Returns:
            积冰厚度值（mm）或None
        """
        if not thickness_str or thickness_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        thickness_str = thickness_str.strip()
        
        try:
            # 0.1mm精度格式
            if len(thickness_str) == 3 and thickness_str.isdigit():
                return float(thickness_str) / 10.0
            
            # 直接数值
            if thickness_str.isdigit():
                return float(thickness_str)
            
            # 包含小数点
            if '.' in thickness_str:
                return float(thickness_str)
                
        except ValueError:
            pass
            
        return None
    
    def _convert_frozen_depth(self, depth_str: str) -> Optional[float]:
        """
        转换冻土深度值
        
        Args:
            depth_str: 冻土深度字符串
            
        Returns:
            冻土深度值（cm）或None
        """
        if not depth_str or depth_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        depth_str = depth_str.strip()
        
        try:
            # 直接厘米数值
            if depth_str.isdigit():
                return float(depth_str)
            
            # 包含小数点
            if '.' in depth_str:
                return float(depth_str)
                
        except ValueError:
            pass
            
        return None
    
    def _convert_sunshine_hours(self, hours_str: str) -> Optional[float]:
        """
        转换日照时数值
        
        Args:
            hours_str: 日照时数字符串
            
        Returns:
            日照时数值（小时）或None
        """
        if not hours_str or hours_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        hours_str = hours_str.strip()
        
        try:
            # 0.1小时精度格式（如125表示12.5小时）
            if len(hours_str) == 3 and hours_str.isdigit():
                return float(hours_str) / 10.0
            
            # 直接数值
            if hours_str.isdigit():
                return float(hours_str)
            
            # 包含小数点
            if '.' in hours_str:
                return float(hours_str)
                
        except ValueError:
            pass
            
        return None
    
    def _convert_grass_temperature(self, temp_str: str) -> Optional[float]:
        """
        转换草面温度值
        
        Args:
            temp_str: 草面温度字符串
            
        Returns:
            草面温度值（°C）或None
        """
        if not temp_str or temp_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        temp_str = temp_str.strip()
        
        try:
            # 负温度（如-042表示-4.2°C）
            if temp_str.startswith('-') and temp_str[1:].isdigit():
                value = float(temp_str[1:])
                if len(temp_str[1:]) == 3:  # 0.1°C精度
                    value = value / 10.0
                return -value
            
            # 正温度，0.1°C精度（如042表示4.2°C）
            if len(temp_str) == 3 and temp_str.isdigit():
                return float(temp_str) / 10.0
            
            # 正温度，带符号（如+042）
            if temp_str.startswith('+'):
                value_str = temp_str[1:]
                if value_str.isdigit():
                    if len(value_str) == 3:
                        return float(value_str) / 10.0
                    return float(value_str)
            
            # 直接数值
            if temp_str.isdigit():
                return float(temp_str)
                
        except ValueError:
            pass
            
        return None
    
    def _parse_evaporation_line(self, line: str, mode: str) -> Dict[str, Any]:
        """解析蒸发量数据行"""
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.element_modes["E"].get(mode, f"方式{mode}"),
            "evaporation_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        for value in values:
            if "=" in value:  # 连接符号
                result["has_connection"] = True
                continue
            
            evap_value = self._convert_evaporation_value(value)
            evap_point = {
                "value": evap_value,
                "unit": "mm",
                "raw_value": value
            }
            
            result["evaporation_data"].append(evap_point)
            
            if evap_value is not None:
                result["valid_count"] += 1
            else:
                result["missing_count"] += 1
        
        return result
    
    def _parse_snow_line(self, line: str, mode: str) -> Dict[str, Any]:
        """解析积雪数据行"""
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.element_modes["S"].get(mode, f"方式{mode}"),
            "snow_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        for value in values:
            if "=" in value:  # 连接符号
                result["has_connection"] = True
                continue
            
            if mode == "A":  # 雪况描述
                condition = self.snow_conditions.get(value, value)
                snow_point = {
                    "condition": condition,
                    "code": value,
                    "raw_value": value
                }
                if condition != value:
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
            else:  # 积雪深度或重量
                depth_value = self._convert_snow_depth(value)
                snow_point = {
                    "depth": depth_value,
                    "unit": "cm" if mode == "0" else "kg/m²",
                    "raw_value": value
                }
                if depth_value is not None:
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
            
            result["snow_data"].append(snow_point)
        
        return result
    
    def _parse_ice_line(self, line: str, mode: str) -> Dict[str, Any]:
        """解析电线积冰数据行"""
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.element_modes["G"].get(mode, f"方式{mode}"),
            "ice_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        for value in values:
            if "=" in value:  # 连接符号
                result["has_connection"] = True
                continue
            
            if mode == "A":  # 积冰类型
                ice_type = self.ice_types.get(value, value)
                ice_point = {
                    "type": ice_type,
                    "code": value,
                    "raw_value": value
                }
                if ice_type != value:
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
            else:  # 积冰厚度或重量
                thickness_value = self._convert_ice_thickness(value)
                ice_point = {
                    "thickness": thickness_value,
                    "unit": "mm" if mode == "0" else "g/m",
                    "raw_value": value
                }
                if thickness_value is not None:
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
            
            result["ice_data"].append(ice_point)
        
        return result
    
    def _parse_frozen_soil_line(self, line: str, mode: str) -> Dict[str, Any]:
        """解析冻土深度数据行"""
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.element_modes["A"].get(mode, f"方式{mode}"),
            "frozen_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        for value in values:
            if "=" in value:  # 连接符号
                result["has_connection"] = True
                continue
            
            if mode == "A":  # 冻土状态
                soil_state = self.frozen_soil_states.get(value, value)
                frozen_point = {
                    "state": soil_state,
                    "code": value,
                    "raw_value": value
                }
                if soil_state != value:
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
            else:  # 冻土深度
                depth_value = self._convert_frozen_depth(value)
                frozen_point = {
                    "depth": depth_value,
                    "unit": "cm",
                    "type": "冻结深度" if mode == "0" else "解冻深度",
                    "raw_value": value
                }
                if depth_value is not None:
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
            
            result["frozen_data"].append(frozen_point)
        
        return result
    
    def _parse_sunshine_line(self, line: str, mode: str) -> Dict[str, Any]:
        """解析日照时数数据行"""
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.element_modes["J"].get(mode, f"方式{mode}"),
            "sunshine_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        for value in values:
            if "=" in value:  # 连接符号
                result["has_connection"] = True
                continue
            
            hours_value = self._convert_sunshine_hours(value)
            sunshine_point = {
                "hours": hours_value,
                "unit": "小时",
                "type": "实际日照" if mode == "0" else ("可照时数" if mode == "1" else "日照百分率"),
                "raw_value": value
            }
            
            result["sunshine_data"].append(sunshine_point)
            
            if hours_value is not None:
                result["valid_count"] += 1
            else:
                result["missing_count"] += 1
        
        return result
    
    def _parse_grass_temperature_line(self, line: str, mode: str) -> Dict[str, Any]:
        """解析草面温度数据行"""
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.element_modes["B"].get(mode, f"方式{mode}"),
            "grass_temperature_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        for value in values:
            if "=" in value:  # 连接符号
                result["has_connection"] = True
                continue
            
            temp_value = self._convert_grass_temperature(value)
            temp_point = {
                "temperature": temp_value,
                "unit": "°C",
                "type": "定时草温" if mode == "0" else ("日极值草温" if mode == "1" else "月极值草温"),
                "raw_value": value
            }
            
            result["grass_temperature_data"].append(temp_point)
            
            if temp_value is not None:
                result["valid_count"] += 1
            else:
                result["missing_count"] += 1
        
        return result
    
    def parse(self, lines: List[str], element_code: str, mode: str, context: ParseContext) -> ElementData:
        """
        解析其他要素数据
        
        Args:
            lines: 数据行列表
            element_code: 要素代码（E/S/G/A/J/B）
            mode: 方式位
            context: 解析上下文
            
        Returns:
            解析结果
        """
        self.logger.debug(f"开始解析{element_code}数据，方式位: {mode}, 行数: {len(lines)}")
        
        # 验证要素代码
        if element_code not in self.supported_elements:
            raise DataValidationError(f"不支持的其他要素代码: {element_code}")
        
        element_name = self.supported_elements[element_code]
        
        # 验证方式位
        if mode not in self.element_modes.get(element_code, {}):
            raise DataValidationError(f"要素{element_code}不支持方式位: {mode}")
        
        result = ElementData(
            element_code=element_code,
            element_name=element_name,
            mode=mode,
            segments=[],
            quality_codes=None,
            errors=[],
            warnings=[]
        )
        
        # 统计信息
        total_values = 0
        valid_values = 0
        data_segments = []
        
        # 选择解析函数
        parse_func_map = {
            "E": self._parse_evaporation_line,
            "S": self._parse_snow_line,
            "G": self._parse_ice_line,
            "A": self._parse_frozen_soil_line,
            "J": self._parse_sunshine_line,
            "B": self._parse_grass_temperature_line
        }
        
        parse_func = parse_func_map[element_code]
        
        # 解析每行数据
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 跳过空行和分隔行
            if not line or line in ["=", "==", "==="] or line.isspace():
                continue
            
            try:
                segment = parse_func(line, mode)
                segment["line_number"] = line_num
                data_segments.append(segment)
                
                total_values += segment["valid_count"] + segment["missing_count"]
                valid_values += segment["valid_count"]
                
            except Exception as e:
                error_msg = f"第{line_num}行解析错误: {str(e)}"
                result.errors.append(error_msg)
                self.logger.error(error_msg)
        
        # 添加数据段
        result.segments.extend(data_segments)
        
        # 数据完整性检查
        if total_values > 0:
            completeness = (valid_values / total_values) * 100
            if completeness < 80:
                warning = f"{element_name}数据完整性较低: {completeness:.1f}%"
                result.warnings.append(warning)
        
        # 特殊验证
        self._validate_element_data(result, element_code)
        
        # 生成统计信息段
        if data_segments:
            summary_segment = {
                "summary": True,
                "total_values": total_values,
                "valid_values": valid_values,
                "missing_values": total_values - valid_values,
                "completeness_percent": (valid_values / total_values * 100) if total_values > 0 else 0,
                "element_info": {
                    "code": element_code,
                    "name": element_name,
                    "mode": mode,
                    "mode_description": self.element_modes[element_code].get(mode)
                }
            }
            result.segments.append(summary_segment)
        
        if not data_segments:
            result.warnings.append(f"未找到有效的{element_name}数据段")
        
        self.logger.info(f"{element_name}数据解析完成: 方式位{mode}, 有效段数{len(data_segments)}, 错误数{len(result.errors)}")
        
        return result
    
    def _validate_element_data(self, result: ElementData, element_code: str) -> None:
        """验证要素数据"""
        for segment in result.segments:
            if element_code == "E":  # 蒸发量验证
                if "evaporation_data" in segment:
                    for evap_point in segment["evaporation_data"]:
                        value = evap_point.get("value")
                        if value is not None and value > 50:  # 日蒸发量大于50mm异常
                            result.warnings.append(f"检测到异常大蒸发量: {value}mm")
            
            elif element_code == "S":  # 积雪验证
                if "snow_data" in segment:
                    for snow_point in segment["snow_data"]:
                        depth = snow_point.get("depth")
                        if depth is not None and depth > 500:  # 积雪深度大于500cm异常
                            result.warnings.append(f"检测到异常厚积雪: {depth}cm")
            
            elif element_code == "G":  # 电线积冰验证
                if "ice_data" in segment:
                    for ice_point in segment["ice_data"]:
                        thickness = ice_point.get("thickness")
                        if thickness is not None and thickness > 100:  # 积冰厚度大于100mm异常
                            result.warnings.append(f"检测到异常厚积冰: {thickness}mm")
            
            elif element_code == "A":  # 冻土深度验证
                if "frozen_data" in segment:
                    for frozen_point in segment["frozen_data"]:
                        depth = frozen_point.get("depth")
                        if depth is not None and depth > 1000:  # 冻土深度大于1000cm异常
                            result.warnings.append(f"检测到异常深冻土: {depth}cm")
            
            elif element_code == "J":  # 日照时数验证
                if "sunshine_data" in segment:
                    for sunshine_point in segment["sunshine_data"]:
                        hours = sunshine_point.get("hours")
                        if hours is not None and hours > 24:  # 日照时数大于24小时异常
                            result.warnings.append(f"检测到异常长日照: {hours}小时")
            
            elif element_code == "B":  # 草面温度验证
                if "grass_temperature_data" in segment:
                    for temp_point in segment["grass_temperature_data"]:
                        temperature = temp_point.get("temperature")
                        if temperature is not None:
                            if temperature < -60 or temperature > 80:  # 异常草面温度
                                result.warnings.append(f"检测到异常草面温度: {temperature}°C") 