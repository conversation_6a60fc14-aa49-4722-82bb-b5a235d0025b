"""
数据库连接管理器

支持SQLite、PostgreSQL、MySQL等多种数据库
"""
import sqlite3
import logging
from pathlib import Path
from typing import Optional, Union, Dict, Any
from contextlib import contextmanager
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_type: str  # sqlite, postgresql, mysql
    database: str  # 数据库名称或文件路径
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    schema: Optional[str] = None
    connection_options: Optional[Dict[str, Any]] = None


class DatabaseConnection:
    """数据库连接管理器"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self._connection = None
        
    def connect(self):
        """建立数据库连接"""
        if self.config.db_type.lower() == 'sqlite':
            return self._connect_sqlite()
        elif self.config.db_type.lower() == 'postgresql':
            return self._connect_postgresql()
        elif self.config.db_type.lower() == 'mysql':
            return self._connect_mysql()
        else:
            raise ValueError(f"不支持的数据库类型: {self.config.db_type}")
    
    def _connect_sqlite(self):
        """连接SQLite数据库"""
        try:
            # 确保目录存在
            db_path = Path(self.config.database)
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            self._connection = sqlite3.connect(
                self.config.database,
                check_same_thread=False
            )
            
            # 启用外键约束
            self._connection.execute("PRAGMA foreign_keys = ON")
            
            logger.info(f"已连接到SQLite数据库: {self.config.database}")
            return self._connection
            
        except Exception as e:
            logger.error(f"连接SQLite数据库失败: {e}")
            raise
    
    def _connect_postgresql(self):
        """连接PostgreSQL数据库"""
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor
            
            connection_params = {
                'host': self.config.host,
                'port': self.config.port or 5432,
                'database': self.config.database,
                'user': self.config.username,
                'password': self.config.password,
                'cursor_factory': RealDictCursor
            }
            
            if self.config.connection_options:
                connection_params.update(self.config.connection_options)
            
            self._connection = psycopg2.connect(**connection_params)
            
            logger.info(f"已连接到PostgreSQL数据库: {self.config.database}")
            return self._connection
            
        except ImportError:
            raise ImportError("请安装psycopg2: pip install psycopg2-binary")
        except Exception as e:
            logger.error(f"连接PostgreSQL数据库失败: {e}")
            raise
    
    def _connect_mysql(self):
        """连接MySQL数据库"""
        try:
            import pymysql
            
            connection_params = {
                'host': self.config.host,
                'port': self.config.port or 3306,
                'database': self.config.database,
                'user': self.config.username,
                'password': self.config.password,
                'charset': 'utf8mb4',
                'cursorclass': pymysql.cursors.DictCursor
            }
            
            if self.config.connection_options:
                connection_params.update(self.config.connection_options)
            
            self._connection = pymysql.connect(**connection_params)
            
            logger.info(f"已连接到MySQL数据库: {self.config.database}")
            return self._connection
            
        except ImportError:
            raise ImportError("请安装PyMySQL: pip install pymysql")
        except Exception as e:
            logger.error(f"连接MySQL数据库失败: {e}")
            raise
    
    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        if not self._connection:
            self.connect()
        
        try:
            if self.config.db_type.lower() == 'sqlite':
                yield self._connection
                self._connection.commit()
            else:
                with self._connection.cursor() as cursor:
                    yield cursor
                self._connection.commit()
        except Exception as e:
            self._connection.rollback()
            logger.error(f"事务回滚: {e}")
            raise
    
    def execute(self, sql: str, params: Optional[tuple] = None):
        """执行SQL语句"""
        if not self._connection:
            self.connect()
        
        cursor = self._connection.cursor()
        try:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            return cursor
        except Exception as e:
            logger.error(f"执行SQL失败: {sql}, 错误: {e}")
            raise
    
    def executemany(self, sql: str, params_list):
        """批量执行SQL语句"""
        if not self._connection:
            self.connect()
        
        cursor = self._connection.cursor()
        try:
            cursor.executemany(sql, params_list)
            return cursor
        except Exception as e:
            logger.error(f"批量执行SQL失败: {sql}, 错误: {e}")
            raise
    
    def fetchall(self, sql: str, params: Optional[tuple] = None):
        """查询所有结果"""
        cursor = self.execute(sql, params)
        return cursor.fetchall()
    
    def fetchone(self, sql: str, params: Optional[tuple] = None):
        """查询单个结果"""
        cursor = self.execute(sql, params)
        return cursor.fetchone()
    
    def close(self):
        """关闭数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None
            logger.info("数据库连接已关闭")
    
    def __enter__(self):
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def create_database_config(
    db_type: str = 'sqlite',
    database: str = 'weather_data.db',
    **kwargs
) -> DatabaseConfig:
    """创建数据库配置的便捷函数"""
    return DatabaseConfig(
        db_type=db_type,
        database=database,
        **kwargs
    ) 