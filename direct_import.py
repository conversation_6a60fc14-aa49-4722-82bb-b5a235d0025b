#!/usr/bin/env python
"""
直接导入气象数据脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
weather_parser_path = project_root / "weather_parser" / "src"
sys.path.insert(0, str(weather_parser_path))

print(f"项目根目录: {project_root}")
print(f"Weather Parser路径: {weather_parser_path}")

# 直接导入核心模块，避免复杂的子模块导入
try:
    print("正在导入核心模块...")
    from weather_parser.database.connection import DatabaseConnection, DatabaseConfig
    from weather_parser.database.importer import DatabaseImporter  
    from weather_parser.parser import WeatherFileParser
    from weather_parser.models import ParseStatus
    print("✅ 成功导入核心模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

# 检查数据目录
data_dir = Path("data")
if not data_dir.exists():
    print(f"❌ 数据目录不存在: {data_dir}")
    sys.exit(1)

txt_files = list(data_dir.glob("*.TXT"))
print(f"📁 发现 {len(txt_files)} 个数据文件")

if len(txt_files) > 0:
    print("前5个文件:")
    for f in txt_files[:5]:
        print(f"  - {f.name}")

# 开始导入
try:
    print("\n🚀 开始导入数据...")
    
    # 创建数据库配置
    db_config = DatabaseConfig(
        db_type="sqlite",
        database="weather_data.db"
    )
    
    print("📦 创建数据库连接...")
    db_connection = DatabaseConnection(db_config)
    
    print("🔧 创建导入器...")
    importer = DatabaseImporter(db_connection)
    
    print("🗄️ 初始化数据库表...")
    importer.initialize_database()
    
    print("🔍 创建解析器...")
    parser = WeatherFileParser()
    
    # 先测试一个文件
    if txt_files:
        test_file = txt_files[0]
        print(f"🧪 测试解析文件: {test_file.name}")
        
        try:
            result = parser.parse_file(test_file)
            
            if result.status == ParseStatus.SUCCESS or result.status == ParseStatus.WARNING:
                print("✅ 文件解析成功")
                print(f"  - 台站信息: {result.station_info}")
                print(f"  - 气象要素数量: {len(result.elements_data)}")
                
                print("💾 测试导入到数据库...")
                if importer.import_parse_result(result):
                    print("✅ 数据导入成功")
                else:
                    print("❌ 数据导入失败")
            else:
                print(f"❌ 文件解析失败: {result.errors}")
        except Exception as e:
            print(f"❌ 测试文件处理错误: {e}")
            import traceback
            traceback.print_exc()
    
    # 如果测试成功，开始批量导入
    print("\n🎯 开始批量导入...")
    successful = 0
    failed = 0
    
    for i, file_path in enumerate(txt_files):
        if i % 20 == 0:  # 每20个文件显示一次进度
            print(f"进度: {i}/{len(txt_files)} ({i/len(txt_files)*100:.1f}%)")
        
        try:
            result = parser.parse_file(file_path)
            if result.status == ParseStatus.SUCCESS or result.status == ParseStatus.WARNING:
                if importer.import_parse_result(result):
                    successful += 1
                else:
                    failed += 1
            else:
                failed += 1
                if i < 5:  # 只显示前5个错误的详细信息
                    print(f"解析失败 {file_path.name}: {result.errors}")
        except Exception as e:
            failed += 1
            if i < 5:  # 只显示前5个错误的详细信息
                print(f"处理文件错误 {file_path.name}: {e}")
    
    print("\n" + "="*50)
    print(f"📈 导入完成!")
    print(f"✅ 成功: {successful} 个文件")
    print(f"❌ 失败: {failed} 个文件")
    print(f"📊 总计: {len(txt_files)} 个文件")
    print(f"💾 数据库: weather_data.db")
    
    # 显示数据库文件大小
    db_file = Path("weather_data.db")
    if db_file.exists():
        size_mb = db_file.stat().st_size / (1024 * 1024)
        print(f"📦 数据库大小: {size_mb:.1f} MB")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc() 