"""
A文件解析器测试模块
包含各种测试用例来验证解析器的正确性
"""

import unittest
import os
from weather_parser import AFileParser
from utils import *
from data_structures import *


class TestAFileParser(unittest.TestCase):
    """A文件解析器测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.parser = AFileParser()
        self.test_file = "data/A54424-195903.TXT"
    
    def test_file_exists(self):
        """测试文件是否存在"""
        self.assertTrue(os.path.exists(self.test_file), f"测试文件不存在: {self.test_file}")
    
    def test_parse_basic(self):
        """测试基本解析功能"""
        if not os.path.exists(self.test_file):
            self.skipTest(f"测试文件不存在: {self.test_file}")
        
        data = self.parser.parse_file(self.test_file)
        
        # 验证基本结构
        self.assertIsInstance(data, AFileData)
        self.assertIsInstance(data.station_info, StationInfo)
        self.assertIsInstance(data.weather_elements, dict)
        
        # 验证台站信息
        self.assertEqual(data.station_info.station_id, "54424")
        self.assertEqual(data.station_info.year, 1959)
        self.assertEqual(data.station_info.month, 3)
    
    def test_station_info_parsing(self):
        """测试台站信息解析"""
        if not os.path.exists(self.test_file):
            self.skipTest(f"测试文件不存在: {self.test_file}")
        
        data = self.parser.parse_file(self.test_file)
        station = data.station_info
        
        # 验证各字段
        self.assertEqual(station.station_id, "54424")
        self.assertEqual(station.latitude, "4008N")
        self.assertEqual(station.longitude, "11706E")
        self.assertEqual(station.year, 1959)
        self.assertEqual(station.month, 3)
    
    def test_weather_elements(self):
        """测试气象要素解析"""
        if not os.path.exists(self.test_file):
            self.skipTest(f"测试文件不存在: {self.test_file}")
        
        data = self.parser.parse_file(self.test_file)
        
        # 验证要素存在
        self.assertIn('T', data.weather_elements)  # 气温
        self.assertIn('P', data.weather_elements)  # 气压
        
        # 验证气温要素
        temp_element = data.weather_elements['T']
        self.assertEqual(temp_element.element_code, 'T')
        self.assertIsInstance(temp_element.data_segments, list)


class TestUtilityFunctions(unittest.TestCase):
    """工具函数测试类"""
    
    def test_parse_latitude(self):
        """测试纬度解析"""
        lat_info = parse_latitude("4008N")
        self.assertEqual(lat_info['degrees'], "40")
        self.assertEqual(lat_info['minutes'], "08")
        self.assertEqual(lat_info['direction'], "N")
        self.assertAlmostEqual(lat_info['decimal'], 40.133, places=3)
    
    def test_parse_longitude(self):
        """测试经度解析"""
        lon_info = parse_longitude("11706E")
        self.assertEqual(lon_info['degrees'], "117")
        self.assertEqual(lon_info['minutes'], "06")
        self.assertEqual(lon_info['direction'], "E")
        self.assertAlmostEqual(lon_info['decimal'], 117.1, places=3)
    
    def test_parse_height(self):
        """测试高度解析"""
        height_info = parse_height("000266")
        self.assertEqual(height_info['parameter'], "0")
        self.assertEqual(height_info['value'], 26.6)
        self.assertEqual(height_info['unit'], "m")
    
    def test_parse_observation_mode(self):
        """测试观测方式解析"""
        mode_info = parse_observation_mode("S02")
        self.assertEqual(mode_info['observation_method'], "0")
        self.assertEqual(mode_info['station_type'], "2")
        self.assertEqual(mode_info['method_description'], "人工观测")
        self.assertEqual(mode_info['type_description'], "基本站")
    
    def test_validate_filename(self):
        """测试文件名验证"""
        self.assertTrue(validate_filename("A54424-195903.TXT"))
        self.assertTrue(validate_filename("AA1531-201705.TXT"))
        self.assertFalse(validate_filename("invalid_filename.txt"))
        self.assertFalse(validate_filename("A54424-195903.txt"))  # 小写扩展名
    
    def test_extract_date_from_filename(self):
        """测试从文件名提取日期"""
        year, month = extract_date_from_filename("A54424-195903.TXT")
        self.assertEqual(year, 1959)
        self.assertEqual(month, 3)
    
    def test_clean_data_value(self):
        """测试数据值清理"""
        self.assertEqual(clean_data_value("////"), "MISSING")
        self.assertEqual(clean_data_value(",,,,"), "TRACE")
        self.assertEqual(clean_data_value("0123"), "0123")
    
    def test_parse_quality_control_code(self):
        """测试质量控制码解析"""
        qc_info = parse_quality_control_code("123")
        self.assertEqual(qc_info['station'], 1)
        self.assertEqual(qc_info['province'], 2)
        self.assertEqual(qc_info['national'], 3)


class TestDataStructures(unittest.TestCase):
    """数据结构测试类"""
    
    def test_station_info_creation(self):
        """测试台站信息创建"""
        station = StationInfo(
            station_id="54424",
            latitude="4008N",
            longitude="11706E",
            observation_field_height="000266",
            pressure_sensor_height="000276",
            wind_sensor_height="///",
            platform_height="///",
            observation_mode="S02",
            observation_items="90999090000909099099",
            quality_control_flag="0",
            year=1959,
            month=3
        )
        
        self.assertEqual(station.station_id, "54424")
        self.assertEqual(station.year, 1959)
        self.assertEqual(station.month, 3)
    
    def test_weather_element_creation(self):
        """测试气象要素创建"""
        element = WeatherElement(
            element_code="T",
            mode_code="0",
            data_segments=[
                ["0250 0274 0329 0222"],
                ["0232 0246 0303 0291"]
            ]
        )
        
        self.assertEqual(element.element_code, "T")
        self.assertEqual(element.mode_code, "0")
        self.assertEqual(len(element.data_segments), 2)
    
    def test_afile_data_methods(self):
        """测试AFileData方法"""
        station = StationInfo(
            station_id="54424", latitude="4008N", longitude="11706E",
            observation_field_height="000266", pressure_sensor_height="000276",
            wind_sensor_height="///", platform_height="///", observation_mode="S02",
            observation_items="90999090000909099099", quality_control_flag="0",
            year=1959, month=3
        )
        
        elements = {
            "T": WeatherElement("T", "0", []),
            "P": WeatherElement("P", "3", [])
        }
        
        data = AFileData(
            station_info=station,
            weather_elements=elements,
            quality_control=None,
            additional_info=None,
            filename="test.txt",
            file_size=1000,
            parse_time=datetime.now()
        )
        
        # 测试方法
        self.assertIsNotNone(data.get_element("T"))
        self.assertIsNone(data.get_element("X"))
        
        element_names = data.get_element_names()
        self.assertIn("T: 气温", element_names)
        self.assertIn("P: 气压", element_names)


def run_integration_test():
    """运行集成测试"""
    print("运行集成测试...")
    
    parser = AFileParser()
    test_files = [
        "data/A54424-195903.TXT",
        "data/A54424-195908.TXT"
    ]
    
    for filepath in test_files:
        if not os.path.exists(filepath):
            print(f"跳过不存在的文件: {filepath}")
            continue
        
        print(f"\n测试文件: {filepath}")
        
        try:
            data = parser.parse_file(filepath)
            
            print(f"✓ 解析成功")
            print(f"  台站: {data.station_info.station_id}")
            print(f"  时间: {data.station_info.year}-{data.station_info.month:02d}")
            print(f"  要素数量: {len(data.weather_elements)}")
            
            # 验证主要要素
            main_elements = ['P', 'T', 'U', 'R']
            for element_code in main_elements:
                if element_code in data.weather_elements:
                    element = data.weather_elements[element_code]
                    print(f"  {element_code}: {len(element.data_segments)}段数据")
            
        except Exception as e:
            print(f"✗ 解析失败: {e}")


if __name__ == "__main__":
    print("A文件解析器测试")
    print("================")
    
    # 运行单元测试
    print("\n运行单元测试...")
    unittest.main(verbosity=2, exit=False)
    
    # 运行集成测试
    print("\n" + "="*50)
    run_integration_test()
